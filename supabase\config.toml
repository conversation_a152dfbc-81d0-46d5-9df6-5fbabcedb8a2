# Supabase Configuration File

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_signup = true

[functions.mailgun-webhook]
verify_jwt = false

[functions.send-contact-email]
verify_jwt = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[studio]
enabled = true
api_url = "http://localhost:54321"

[inbucket]
enabled = true
smtp_port = 54325
pop3_port = 54326
