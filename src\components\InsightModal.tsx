'use client'

import { useEffect } from 'react'
import { Insight } from '@/lib/supabase'

interface InsightModalProps {
  insight: Insight | null
  isOpen: boolean
  onClose: () => void
}

export default function InsightModal({
  insight,
  isOpen,
  onClose,
}: InsightModalProps) {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen || !insight) return null

  return (
    <div className="insight-modal-overlay" onClick={onClose}>
      <div className="insight-modal-content" onClick={e => e.stopPropagation()}>
        <div className="insight-modal-header">
          <div className="insight-modal-meta">
            <span className="insight-category">{insight.category}</span>
            <span className="insight-read-time">{insight.read_time}</span>
          </div>
          <button
            className="insight-modal-close"
            onClick={onClose}
            aria-label="Close modal"
          >
            ×
          </button>
        </div>

        <div className="insight-modal-body">
          <h1 className="insight-modal-title">{insight.title}</h1>

          <div className="insight-modal-description">
            <p>{insight.description}</p>
          </div>

          {insight.full_content && (
            <div className="insight-modal-full-content">
              <div className="content-divider"></div>
              <div
                className="insight-content"
                dangerouslySetInnerHTML={{ __html: insight.full_content }}
              />
            </div>
          )}
        </div>

        <div className="insight-modal-footer">
          <div className="insight-published-date">
            Published{' '}
            {new Date(insight.published_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
