'use client'

import { useState } from 'react'

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const faqs = [
    {
      question: 'What types of business processes can be automated with AI?',
      answer:
        'I can automate a wide range of processes including customer service (chatbots), lead qualification and nurturing, email marketing sequences, data entry and processing, appointment scheduling, social media management, and sales pipeline management. Essentially, any repetitive task that follows a logical pattern can be automated.',
    },
    {
      question:
        'How do you determine which automations are right for my business?',
      answer:
        'I start with a comprehensive audit of your current processes to identify bottlenecks and repetitive tasks. Then I analyze your business goals, customer journey, and pain points to prioritize automations that will deliver the highest ROI. We focus on quick wins first, then build more complex systems over time.',
    },
    {
      question: "What's the typical timeline for implementing AI automation?",
      answer:
        "Simple automations like chatbots or email sequences can be implemented in 1-2 weeks. More complex systems involving multiple integrations typically take 4-8 weeks. I work in phases, so you'll see results quickly while we build out the complete system.",
    },
    {
      question: 'Do you provide ongoing support and maintenance?',
      answer:
        'Yes, I offer comprehensive support packages that include system monitoring, updates, optimization, and troubleshooting. AI systems need regular fine-tuning to maintain peak performance, and I ensure your automations continue to deliver results as your business grows.',
    },
    {
      question: 'What kind of results can I expect from AI automation?',
      answer:
        'Results vary by business, but typical outcomes include 50-80% reduction in manual tasks, 24/7 customer support availability, 200-400% improvement in response times, and 30-60% increase in lead conversion rates. I provide detailed analytics to track ROI and continuously optimize performance.',
    },
    {
      question: 'How much does AI automation cost?',
      answer:
        'Investment depends on the complexity and scope of your automation needs. Simple chatbots start around $100, while comprehensive automation systems range from $500-$20,000. I offer flexible payment plans and the ROI typically pays for the investment within 2-6 months.',
    },
  ]

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section id="faq" className="section">
      <div className="container">
        <div className="section-header">
          <h2>Frequently Asked Questions</h2>
          <p>Everything you need to know about AI automation</p>
        </div>

        <div className="faq-container">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className={`faq-item ${openIndex === index ? 'active' : ''}`}
            >
              <button className="faq-question" onClick={() => toggleFAQ(index)}>
                <span>{faq.question}</span>
                <span className="faq-icon">
                  {openIndex === index ? '−' : '+'}
                </span>
              </button>
              <div className="faq-answer">
                <p>{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
