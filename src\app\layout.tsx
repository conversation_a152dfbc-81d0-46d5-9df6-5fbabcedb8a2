import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : 'http://localhost:3000'
  ),
  title: '<PERSON> | AI & Business Automation Specialist',
  description:
    'Transform your business with AI-powered automations. <PERSON> helps businesses achieve extraordinary growth through strategic AI automation systems.',
  keywords:
    'AI automation, business automation, process optimization, <PERSON>, AI expert',
  authors: [{ name: '<PERSON>' }],
  creator: '<PERSON>',
  publisher: '<PERSON>',
  openGraph: {
    title: '<PERSON> | AI & Business Automation Specialist',
    description:
      'Transform your business with AI-powered automations. Strategic AI automation systems that work 24/7 to drive results.',
    url: 'https://deniserastus.com',
    siteName: '<PERSON>folio',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON> - AI Automation Expert',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '<PERSON> | AI & Business Automation Specialist',
    description: 'Transform your business with AI-powered automations.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [{ rel: 'mask-icon', url: '/favicon.svg', color: '#06b6d4' }],
  },
  manifest: '/site.webmanifest',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* Favicon Links */}
        <link rel="icon" href="/favicon.ico" sizes="32x32" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/site.webmanifest" />

        {/* Theme and Browser Configuration */}
        <meta name="theme-color" content="#06b6d4" />
        <meta name="msapplication-TileColor" content="#0f172a" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* Safari Pinned Tab */}
        <link rel="mask-icon" href="/favicon.svg" color="#06b6d4" />
      </head>
      <body>{children}</body>
    </html>
  )
}
