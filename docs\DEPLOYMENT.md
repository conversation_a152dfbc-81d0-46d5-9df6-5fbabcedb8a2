# Deployment Guide - Denis Erast<PERSON> Portfolio

## 🚀 Deployment Overview

This guide covers the complete deployment setup for the Denis Erastus Portfolio using Vercel with GitHub integration.

> **⚠️ Important Branch Strategy Change**: We now use `develop` as the production branch due to enhanced booking system stability and Vercel build compatibility. The `main` branch is archived.

## 🌐 Deployment Environments

### Production Environment

- **URL**: https://my-portfolio-git-develop-devdenis-projects.vercel.app/
- **Branch**: `develop` ⭐ **PRODUCTION BRANCH**
- **Auto-deploy**: ✅ On push to develop
- **Environment**: Production Supabase
- **Status**: ✅ **LIVE AND FULLY FUNCTIONAL**

### Archive Environment

- **URL**: https://my-portfolio-git-main-devdenis-projects.vercel.app/
- **Branch**: `main` (archived)
- **Auto-deploy**: ❌ Disabled due to build issues
- **Environment**: Legacy
- **Status**: ⚠️ **ARCHIVED - NOT MAINTAINED**

## 🔧 Deployment Issue Resolution (COMPLETED)

### Initial Deployment Problem (RESOLVED ✅)

**Issue**: The initial commit to the main branch failed during CI/CD, causing Vercel to create a project with broken configuration.

**Root Cause**:

- Failed initial deployment created cached broken settings in Vercel
- Vercel project was initialized from a broken state
- Subsequent merges couldn't fix the underlying configuration issue

**Solution Applied**:

1. **Deleted** the existing Vercel project completely
2. **Recreated** the Vercel project from scratch
3. **Connected** to the GitHub repository with correct settings
4. **Verified** both staging and production deployments

**Result**: ✅ **Both environments now work perfectly with error-free deployments**

### Workflow Validation ✅

- ✅ **Develop branch** → Production deployment working
- ⚠️ **Main branch** → Archived due to Vercel build compatibility issues
- ✅ **Enhanced booking system** fully functional on develop branch
- ✅ **CI/CD pipeline** fully functional with quality checks
- ✅ **No deployment errors** in current production setup (develop branch)

## 📋 Prerequisites

Before deployment, ensure you have:

- [x] GitHub repository created
- [x] Vercel account (free tier)
- [x] Supabase projects (staging & production)
- [x] Domain name (optional, for production)

## 🔧 Vercel Setup

### 1. Connect Repository

1. **Login to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub

2. **Import Project**

   ```bash
   # From Vercel dashboard
   New Project → Import Git Repository
   → Select: ProDevDenis/MyPortfolio
   ```

3. **Configure Build Settings**
   ```
   Framework Preset: Next.js
   Root Directory: ./
   Build Command: npm run build
   Output Directory: .next
   Install Command: npm install
   ```

### 2. Environment Variables

#### Production Environment Variables

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_prod_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_prod_service_role_key
NODE_ENV=production
```

#### Staging Environment Variables

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-staging-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_staging_service_role_key
NODE_ENV=staging
```

### 3. Domain Configuration

#### Custom Domain (Production)

1. **Add Domain in Vercel**

   ```
   Project Settings → Domains → Add Domain
   → Enter: deniserastus.com
   ```

2. **DNS Configuration**
   ```
   Type: CNAME
   Name: @
   Value: cname.vercel-dns.com
   ```

#### Subdomain (Staging)

```
Type: CNAME
Name: staging
Value: cname.vercel-dns.com
```

## 🔄 GitHub Actions Setup

### 1. Repository Secrets

Add these secrets in GitHub repository settings:

```
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_vercel_org_id
VERCEL_PROJECT_ID=your_vercel_project_id

STAGING_SUPABASE_URL=your_staging_supabase_url
STAGING_SUPABASE_ANON_KEY=your_staging_anon_key

PRODUCTION_SUPABASE_URL=your_production_supabase_url
PRODUCTION_SUPABASE_ANON_KEY=your_production_anon_key
```

### 2. Workflow Configuration

The CI/CD pipeline (`.github/workflows/ci.yml`) handles:

- **Code Quality Checks**
  - ESLint
  - TypeScript checking
  - Prettier formatting
  - Unit tests

- **Automated Deployment**
  - Production: On push to `develop`
  - Archive: `main` branch disabled

### 3. Branch Protection Rules

Configure in GitHub repository settings:

```yaml
Branch Protection Rules:
  main:
    - Require pull request reviews (1)
    - Require status checks to pass
    - Require branches to be up to date
    - Restrict pushes to main branch
    - Include administrators: false

  develop:
    - Require status checks to pass
    - Require branches to be up to date
```

## 🚀 Deployment Process

### Automatic Deployment

1. **Production Deployment**

   ```bash
   # Create feature branch
   git checkout -b feature/new-feature

   # Make changes and commit
   git add .
   git commit -m "Add new feature"

   # Push to develop (PRODUCTION BRANCH)
   git checkout develop
   git merge feature/new-feature
   git push origin develop

   # ✅ Auto-deploys to production
   ```

2. **Archive Branch (main)**

   ```bash
   # Main branch is archived and not used for deployment
   # All development happens on develop branch
   # No automatic deployment from main branch
   ```

### Manual Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to staging
vercel --target staging

# Deploy to production
vercel --prod
```

## 📊 Monitoring & Analytics

### Vercel Analytics

- **Performance Metrics**: Core Web Vitals
- **Usage Analytics**: Page views, unique visitors
- **Error Tracking**: Runtime errors and build failures

### Custom Analytics

- **Database**: Supabase analytics table
- **Real-time**: WebSocket connections
- **Dashboard**: Custom analytics dashboard

## 🔍 Troubleshooting

### Common Deployment Issues

1. **Build Failures**

   ```bash
   # Check build logs in Vercel dashboard
   # Common causes:
   - TypeScript errors
   - Missing environment variables
   - Package dependency issues
   ```

2. **Environment Variable Issues**

   ```bash
   # Verify in Vercel dashboard
   Project Settings → Environment Variables

   # Check variable names match exactly
   # Ensure staging/production separation
   ```

3. **Domain Configuration**
   ```bash
   # DNS propagation can take 24-48 hours
   # Use DNS checker tools
   # Verify CNAME records
   ```

### Rollback Procedures

1. **Automatic Rollback**

   ```bash
   # Vercel automatically keeps previous deployments
   # Use Vercel dashboard to promote previous deployment
   ```

2. **Git Rollback**
   ```bash
   # Revert commit and push
   git revert <commit-hash>
   git push origin main
   ```

## 📈 Performance Optimization

### Build Optimization

```javascript
// next.config.js optimizations
module.exports = {
  swcMinify: true,
  images: {
    formats: ['image/webp', 'image/avif'],
  },
  experimental: {
    optimizeCss: true,
  },
}
```

### Caching Strategy

```javascript
// API route caching
export async function GET() {
  return Response.json(data, {
    headers: {
      'Cache-Control': 's-maxage=60, stale-while-revalidate',
    },
  })
}
```

## 🔐 Security Considerations

### Environment Security

- Never commit `.env` files
- Use different keys for staging/production
- Rotate API keys regularly
- Monitor access logs

### Content Security Policy

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval'",
          },
        ],
      },
    ]
  },
}
```

---

_Deployment Guide Version: 1.0.0_
_Last Updated: 2025-01-01_
