'use client'

import { useState, useEffect } from 'react'
import { db, Insight } from '@/lib/supabase'
import InsightModal from './InsightModal'

export default function Insights() {
  const [insights, setInsights] = useState<Insight[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedInsight, setSelectedInsight] = useState<Insight | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    async function fetchInsights() {
      try {
        setLoading(true)
        const data = await db.insights.getLatest(3)
        setInsights(data)
      } catch (err) {
        console.error('Error fetching insights:', err)
        setError('Failed to load insights')
        // Fallback to hardcoded data if database fails
        setInsights([
          {
            id: 'fallback-1',
            title: 'SEO alone wont help, Be the Answer',
            description:
              "AI is changing how people search and if your brand isn't optimized for Answer Engine Optimization, you're already falling behind. SEO got us here, but AEO is the new frontier.",
            read_time: '5 min read',
            category: 'SEO Updates',
            published_at: new Date().toISOString(),
          },
          {
            id: 'fallback-2',
            title: 'Automating Lead Nurturing with AI',
            description:
              'Discover the exact framework I use to nurture leads automatically while maintaining that personal touch.',
            read_time: '7 min read',
            category: 'Lead Generation',
            published_at: new Date().toISOString(),
          },
          {
            id: 'fallback-3',
            title: 'The ROI of Business Process Automation',
            description:
              'Real numbers from real businesses: How automation delivers measurable returns on investment.',
            read_time: '6 min read',
            category: 'Business Strategy',
            published_at: new Date().toISOString(),
          },
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchInsights()
  }, [])

  const handleReadMore = (insight: Insight) => {
    setSelectedInsight(insight)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedInsight(null)
  }

  if (loading) {
    return (
      <section id="insights" className="section">
        <div className="container">
          <div className="section-header">
            <h2>Latest Insights</h2>
            <p>
              Practical strategies and real-world case studies in AI automation
            </p>
          </div>
          <div className="insights-grid">
            {[1, 2, 3].map(i => (
              <div key={i} className="insight-card loading">
                <div className="insight-category">Loading...</div>
                <h3>Loading insight...</h3>
                <p>Please wait while we fetch the latest insights...</p>
                <div className="insight-meta">
                  <span className="read-time">-- min read</span>
                  <button className="read-more" disabled>
                    Loading...
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <>
      <section id="insights" className="section">
        <div className="container">
          <div className="section-header">
            <h2>Latest Insights</h2>
            <p>
              Practical strategies and real-world case studies in AI automation
            </p>
          </div>

          {error && (
            <div className="error-message">
              <p>⚠️ {error}. Showing cached content.</p>
            </div>
          )}

          <div className="insights-grid">
            {insights.map(insight => (
              <article key={insight.id} className="insight-card">
                <div className="insight-category">{insight.category}</div>
                <h3>{insight.title}</h3>
                <p>{insight.description}</p>
                <div className="insight-meta">
                  <span className="read-time">{insight.read_time}</span>
                  <button
                    className="read-more"
                    onClick={() => handleReadMore(insight)}
                  >
                    Read More →
                  </button>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      <InsightModal
        insight={selectedInsight}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </>
  )
}
