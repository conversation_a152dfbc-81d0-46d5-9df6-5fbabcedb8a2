# Denis Erastus Portfolio

> AI Automation Expert Portfolio - Modern Next.js application with Supabase integration

[![CI/CD Pipeline](https://github.com/ProDevDenis/MyPortfolio/actions/workflows/ci.yml/badge.svg)](https://github.com/ProDevDenis/MyPortfolio/actions/workflows/ci.yml)
[![Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black)](https://vercel.com)
[![Supabase](https://img.shields.io/badge/Database-Supabase-green)](https://supabase.com)

## 🚀 Live Demo

- **Production**: [my-portfolio-git-develop-devdenis-projects.vercel.app](https://my-portfolio-git-develop-devdenis-projects.vercel.app/) ✅
- **Archive**: [my-portfolio-git-main-devdenis-projects.vercel.app](https://my-portfolio-git-main-devdenis-projects.vercel.app/) ⚠️

> **Status**: Production environment (develop branch) is live and fully functional with enhanced booking system!

## 🎯 Project Overview

Professional portfolio website for Denis Erastus, showcasing AI automation expertise and services. Built with modern web technologies and best practices.

### ✨ Features

- **Responsive Design** - Optimized for all devices with dark theme styling
- **AI Automation Focus** - Showcases expertise and case studies
- **Enhanced Booking System** - Two-step Calendly integration with project details pre-filling
- **Contact Management** - Streamlined contact forms with transparent dark theme design
- **Analytics Tracking** - Custom analytics with CTA performance tracking
- **Database Integration** - Complete booking data storage with notes field
- **SEO Optimized** - Server-side rendering for better search visibility
- **Performance Focused** - Sub-2 second loading times

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Custom CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel
- **CI/CD**: GitHub Actions

## 📋 Quick Start

### Prerequisites

- Node.js 18+
- Git
- GitHub account
- Vercel account
- Supabase account

### Installation

```bash
# Clone the repository
git clone https://github.com/ProDevDenis/MyPortfolio.git
cd MyPortfolio

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
MyPortfolio/
├── docs/                     # Documentation
├── src/
│   ├── app/                  # Next.js App Router
│   ├── components/           # React Components
│   ├── lib/                  # Utilities & Configuration
│   └── styles/               # CSS Styles
├── public/                   # Static Assets
├── .github/workflows/        # GitHub Actions
├── tests/                    # Test Files
└── PROJECT_PLAN.md          # Project roadmap
```

## 🔄 Development Workflow

### Branch Strategy

- `develop` - **Production branch** (live deployment)
- `main` - Archive branch (legacy/backup)
- `feature/*` - Feature development
- `hotfix/*` - Critical fixes

> **Note**: We use `develop` as the production branch due to enhanced booking system stability and Vercel compatibility.

### Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run type-check   # TypeScript checking
npm run format       # Format with Prettier

# Testing
npm run test         # Run tests
npm run test:watch   # Watch mode
npm run test:coverage # Coverage report
```

## 🚀 Deployment

### Automatic Deployment

- **Production**: Deploys automatically on push to `develop`
- **Archive**: `main` branch is archived (no automatic deployment)

### Manual Deployment

```bash
# Deploy to staging
vercel --target staging

# Deploy to production
vercel --prod
```

## 🎯 Enhanced Booking System

### Two-Step Booking Flow

1. **Personal Info Collection** - "Let's Talk." form with project details
2. **Calendly Integration** - Pre-filled scheduling with seamless data flow
3. **Database Storage** - Complete booking data with notes field
4. **Custom Success** - Branded confirmation with home page redirect

### Key Features

- **Dark Theme Forms** - Transparent backgrounds with cyan labels
- **Project Details Pre-filling** - Notes automatically appear in Calendly
- **CTA Source Tracking** - Detailed analytics on booking sources
- **Simplified Contact Forms** - Streamlined to essential fields only
- **Custom Success Messages** - Professional branded confirmations

### Form Design

- **Transparent Inputs** - No white backgrounds, cyan borders
- **Colored Labels** - Primary cyan (#17b8dd) for all form labels
- **Responsive Design** - Mobile-optimized with proper spacing
- **Accessibility** - Keyboard navigation and screen reader support

## 📊 Environment Variables

Required environment variables (see `.env.example`):

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_CALENDLY_URL=your_calendly_booking_url
```

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](./docs/) directory:

- [Setup Guide](./docs/SETUP.md)
- [Database Schema](./docs/DATABASE.md)
- [Calendly Integration](./docs/CALENDLY_INTEGRATION_SUMMARY.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)
- [Database Migration Notes](./docs/DATABASE_MIGRATION_NOTES.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

**Denis Erastus**

- Website: [deniserastus.com](https://deniserastus.com)
- Email: <EMAIL>
- GitHub: [@ProDevDenis](https://github.com/ProDevDenis)

---

_Built with ❤️ by Denis Erastus_
