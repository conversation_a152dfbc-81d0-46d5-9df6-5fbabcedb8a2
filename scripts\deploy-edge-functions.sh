#!/bin/bash

# Deploy Edge Functions to Supabase
# This script deploys the email notification Edge Functions

echo "🚀 Deploying Supabase Edge Functions..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if we're logged in
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please login first:"
    echo "supabase login"
    exit 1
fi

# Deploy send-contact-email function
echo "📧 Deploying send-contact-email function..."
supabase functions deploy send-contact-email --project-ref hkbhttezrkjbyoougifv

if [ $? -eq 0 ]; then
    echo "✅ send-contact-email function deployed successfully"
else
    echo "❌ Failed to deploy send-contact-email function"
    exit 1
fi

# Deploy mailgun-webhook function
echo "🎣 Deploying mailgun-webhook function..."
supabase functions deploy mailgun-webhook --project-ref hkbhttezrkjbyoougifv

if [ $? -eq 0 ]; then
    echo "✅ mailgun-webhook function deployed successfully"
else
    echo "❌ Failed to deploy mailgun-webhook function"
    exit 1
fi

echo ""
echo "🎉 All Edge Functions deployed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Set environment variables in Supabase Dashboard:"
echo "   - MAILGUN_API_KEY"
echo "   - MAILGUN_WEBHOOK_SIGNING_KEY"
echo "   - ADMIN_EMAIL (already <NAME_EMAIL>)"
echo "   - MAILGUN_DOMAIN (already set to fokas.tech)"
echo ""
echo "2. Configure Mailgun webhooks to point to:"
echo "   https://hkbhttezrkjbyoougifv.supabase.co/functions/v1/mailgun-webhook"
echo ""
echo "3. Test the contact form to verify everything works!"
echo ""
