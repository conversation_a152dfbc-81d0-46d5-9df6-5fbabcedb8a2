// app/layout.tsx - Add to your Next.js layout
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Your Name - AI Automation Specialist',
  description: 'Professional AI automation services and consulting',
  // Add favicon metadata
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.svg', type: 'image/svg+xml' }
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/favicon.svg',
        color: '#06b6d4'
      }
    ]
  },
  manifest: '/site.webmanifest',
  other: {
    'msapplication-TileColor': '#0f172a',
    'theme-color': '#06b6d4'
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* Additional favicon tags for better compatibility */}
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="mask-icon" href="/favicon.svg" color="#06b6d4" />
        <meta name="msapplication-TileColor" content="#0f172a" />
        <meta name="theme-color" content="#06b6d4" />
      </head>
      <body>{children}</body>
    </html>
  )
}